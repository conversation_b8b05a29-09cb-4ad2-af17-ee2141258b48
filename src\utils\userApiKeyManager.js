// Store user's API key in browser storage
export const saveUserApiKey = (keyName, keyValue) => {
  try {
    // Optional: Basic encryption before storing
    const encryptedKey = btoa(keyValue); // Simple base64 encoding (not truly secure)
    localStorage.setItem(`apiKey_${keyName}`, encryptedKey);
    return true;
  } catch (error) {
    console.error('Error saving API key:', error);
    return false;
  }
};

// Retrieve user's API key
export const getUserApiKey = (keyName) => {
  try {
    const encryptedKey = localStorage.getItem(`apiKey_${keyName}`);
    if (!encryptedKey) return null;
    
    // Decode the key
    return atob(encryptedKey);
  } catch (error) {
    console.error('Error retrieving API key:', error);
    return null;
  }
};

// Remove user's API key
export const removeUserApiKey = (keyName) => {
  try {
    localStorage.removeItem(`apiKey_${keyName}`);
    return true;
  } catch (error) {
    console.error('Error removing API key:', error);
    return false;
  }
};

// Check if user has stored an API key
export const hasUserApiKey = (keyName) => {
  return localStorage.getItem(`apiKey_${keyName}`) !== null;
};
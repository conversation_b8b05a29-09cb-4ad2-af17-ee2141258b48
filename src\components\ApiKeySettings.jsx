import React, { useState, useEffect } from 'react';
import { saveUser<PERSON><PERSON><PERSON><PERSON>, getUser<PERSON><PERSON><PERSON><PERSON>, hasUser<PERSON><PERSON><PERSON><PERSON> } from '../utils/userApiKeyManager';

const ApiKeySettings = () => {
  const [deepseekKey, setDeepseekKey] = useState('');
  const [keyStatus, setKeyStatus] = useState('');
  const [showKey, setShowKey] = useState(false);

  useEffect(() => {
    // Check if key exists on component mount
    if (hasUserApiKey('deepseek')) {
      setKeyStatus('saved');
      setDeepseekKey(getUserApiKey('deepseek'));
    }
  }, []);

  const handleSaveKey = () => {
    if (!deepseekKey.trim()) {
      setKeyStatus('error');
      return;
    }
    
    const success = saveUserApiKey('deepseek', deepseekKey);
    setKeyStatus(success ? 'saved' : 'error');
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">API Key Settings</h2>
      <p className="mb-4 text-gray-600">
        Your API keys are stored securely in your browser and never sent to our servers.
      </p>
      
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          DeepSeek API Key
        </label>
        <div className="flex">
          <input
            type={showKey ? "text" : "password"}
            value={deepseekKey}
            onChange={(e) => setDeepseekKey(e.target.value)}
            className="flex-1 p-2 border rounded-l-md focus:ring-primary-500 focus:border-primary-500"
            placeholder="Enter your DeepSeek API key"
          />
          <button
            onClick={() => setShowKey(!showKey)}
            className="px-3 bg-gray-100 border border-l-0 rounded-r-md"
            type="button"
          >
            {showKey ? "Hide" : "Show"}
          </button>
        </div>
      </div>
      
      <button
        onClick={handleSaveKey}
        className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
      >
        Save API Key
      </button>
      
      {keyStatus === 'saved' && (
        <p className="mt-2 text-green-600">API key saved successfully!</p>
      )}
      {keyStatus === 'error' && (
        <p className="mt-2 text-red-600">Error saving API key. Please try again.</p>
      )}
      
      <div className="mt-6 p-4 bg-yellow-50 rounded-md">
        <h3 className="font-medium text-yellow-800">Important Security Information</h3>
        <ul className="mt-2 text-sm text-yellow-700 list-disc pl-5">
          <li>Your API key is stored only on your device</li>
          <li>We never send your API key to our servers</li>
          <li>All translations are processed directly between your browser and DeepSeek</li>
          <li>Clear your browser data to remove stored keys</li>
        </ul>
      </div>
    </div>
  );
};

export default ApiKeySettings;